import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@radix-ui/react-checkbox";
import { Separator, Label } from "@radix-ui/react-dropdown-menu";
import { Download } from "lucide-react";

export function ScheduleBuilder (){

                <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Work Schedule</CardTitle>
                  <CardDescription>
                    Set the family schedule you would like the Aupair to follow
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="border rounded-lg p-4 bg-muted/50">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Weekly Schedule - Johnson Family</h4>
                    </div>
                    <div className="space-y-3">
                      <div className="grid grid-cols-7 gap-2 text-sm">
                        <div className="font-medium text-center p-2 bg-background rounded">Mon</div>
                        <div className="font-medium text-center p-2 bg-background rounded">Tue</div>
                        <div className="font-medium text-center p-2 bg-background rounded">Wed</div>
                        <div className="font-medium text-center p-2 bg-background rounded">Thu</div>
                        <div className="font-medium text-center p-2 bg-background rounded">Fri</div>
                        <div className="font-medium text-center p-2 bg-background rounded">Sat</div>
                        <div className="font-medium text-center p-2 bg-background rounded">Sun</div>
                      </div>

                      <div className="grid grid-cols-7 gap-2 text-xs">
                        <div className="text-center p-2 border rounded">
                          <div className="font-medium">7:00-9:00</div>
                          <div className="text-muted-foreground">Morning prep</div>
                        </div>
                        <div className="text-center p-2 border rounded">
                          <div className="font-medium">7:00-9:00</div>
                          <div className="text-muted-foreground">Morning prep</div>
                        </div>
                        <div className="text-center p-2 border rounded">
                          <div className="font-medium">7:00-9:00</div>
                          <div className="text-muted-foreground">Morning prep</div>
                        </div>
                        <div className="text-center p-2 border rounded">
                          <div className="font-medium">7:00-9:00</div>
                          <div className="text-muted-foreground">Morning prep</div>
                        </div>
                        <div className="text-center p-2 border rounded">
                          <div className="font-medium">7:00-9:00</div>
                          <div className="text-muted-foreground">Morning prep</div>
                        </div>
                        <div className="text-center p-2 bg-muted rounded">
                          <div className="text-muted-foreground">Free day</div>
                        </div>
                        <div className="text-center p-2 bg-muted rounded">
                          <div className="text-muted-foreground">Free day</div>
                        </div>
                      </div>

                      <div className="grid grid-cols-7 gap-2 text-xs">
                        <div className="text-center p-2 border rounded">
                          <div className="font-medium">15:00-18:00</div>
                          <div className="text-muted-foreground">After school</div>
                        </div>
                        <div className="text-center p-2 border rounded">
                          <div className="font-medium">15:00-18:00</div>
                          <div className="text-muted-foreground">After school</div>
                        </div>
                        <div className="text-center p-2 border rounded">
                          <div className="font-medium">15:00-18:00</div>
                          <div className="text-muted-foreground">After school</div>
                        </div>
                        <div className="text-center p-2 border rounded">
                          <div className="font-medium">15:00-18:00</div>
                          <div className="text-muted-foreground">After school</div>
                        </div>
                        <div className="text-center p-2 border rounded">
                          <div className="font-medium">15:00-18:00</div>
                          <div className="text-muted-foreground">After school</div>
                        </div>
                        <div className="text-center p-2 bg-muted rounded">
                          <div className="text-muted-foreground">Free day</div>
                        </div>
                        <div className="text-center p-2 bg-muted rounded">
                          <div className="text-muted-foreground">Free day</div>
                        </div>
                      </div>
                    </div>

                    <Separator className="my-4" />

                    <div className="space-y-2">
                      <h5 className="font-medium text-sm">Additional Notes:</h5>
                      <p className="text-sm text-muted-foreground">
                        • Total weekly hours: 25 hours • Weekends are free unless special arrangements • Flexible timing
                        during school holidays • One evening babysitting per week (to be arranged)
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <Checkbox id="schedule-submit" />
                    <Label onClick="schedule-submit" className="text-sm">
                      I have reviewed and accept this work schedule
                    </Label>
                  </div>

                  <div className="flex space-x-2">
                    <Button variant="outline" className="flex-1">
                      <Download className="h-4 w-4 mr-2" />
                      Download Schedule
                    </Button>
                    <Button className="flex-1" onClick={() => markStepComplete(step.id)}>
                      Accept Schedule
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
        }
