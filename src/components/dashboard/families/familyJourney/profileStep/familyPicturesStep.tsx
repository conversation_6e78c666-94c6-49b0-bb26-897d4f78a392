import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Upload } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { useState } from 'react';

interface FamilyPicturesProps {
  onSubmit?: (files: File[]) => void;
  uploadedFiles?: File[];
  isCompleted?: boolean;
}

export default function FamilyPicturesStep({ onSubmit, uploadedFiles = [], isCompleted = false }: FamilyPicturesProps) {
  const [files, setFiles] = useState<File[]>(uploadedFiles);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    if (selectedFiles.length + files.length <= 4) {
      setFiles([...files, ...selectedFiles]);
    } else {
      alert('Maximum 4 files allowed');
    }
  };

  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit(files);
    }
    console.log('Family pictures submitted:', files);
  };

  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-stone-200 rounded-md">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <CheckCircle className={`h-6 w-6 ${isCompleted ? 'text-black' : 'text-stone-600'}`} />
              <span className="text-lg font-semibold">2.2 Family Pictures</span>
              <Badge className={isCompleted ? 'bg-black text-white' : 'bg-stone-600 text-white'}>
                {isCompleted ? 'Completed' : 'In Progress'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="md:col-span-2 space-y-6">
                    <div>
                      <h4 className="font-bold text-black text-base mb-3">To-Do:</h4>
                      <p className="text-stone-700 text-base leading-relaxed">
                        Please upload pictures of both parents and the kids.
                      </p>
                      <p className="text-stone-600 mt-3 text-base">
                        Please note: Upload only jpeg's, if you like to make collages then you can use a program like
                        piccolage.
                      </p>
                    </div>

                    {files.length > 0 && (
                      <div>
                        <h4 className="font-bold text-black text-base mb-3">Uploaded Files:</h4>
                        <div className="space-y-2">
                          {files.map((file, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-stone-50 rounded-md">
                              <span className="text-sm text-stone-700">{file.name}</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFile(index)}
                                className="text-red-600 hover:text-red-800"
                              >
                                Remove
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="bg-stone-900 text-white p-8 rounded-md">
                    <h3 className="text-base font-bold mb-6">Family Pictures</h3>
                    <p className="text-sm mb-6 text-stone-300">Upload maximum 4 files ({files.length}/4)</p>

                    <div className="border-2 border-dashed border-white/30 rounded-md p-8 text-center">
                      <Upload className="h-10 w-10 mx-auto mb-3 text-white/70" />
                      <p className="text-sm text-white/90 mb-4">Drag and drop files or click to browse</p>
                      <input
                        type="file"
                        multiple
                        accept="image/jpeg,image/jpg"
                        onChange={handleFileUpload}
                        className="hidden"
                        id="file-upload"
                        disabled={files.length >= 4}
                      />
                      <label
                        htmlFor="file-upload"
                        className={`cursor-pointer inline-block px-4 py-2 bg-white/20 rounded text-sm ${
                          files.length >= 4 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-white/30'
                        }`}
                      >
                        Choose Files
                      </label>
                    </div>

                    <Button
                      className="w-full mt-6 bg-white text-black hover:bg-stone-100"
                      onClick={handleSubmit}
                      disabled={files.length === 0}
                    >
                      SUBMIT
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
