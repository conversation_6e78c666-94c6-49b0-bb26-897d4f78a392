import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, CheckCircle } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { useState, useEffect } from 'react';

interface Child {
  name: string;
  age: string;
  gender: string;
}

interface FamilyFormData {
  familyName: string;
  address: string;
  postalCode: string;
  city: string;
  country: string;
  phone: string;
  email: string;
  children: Child[];
  preferredStartDate: string;
  latestStartDate: string;
}

interface FamilyInformationStepProps {
  formData: FamilyFormData;
  setFormData: React.Dispatch<React.SetStateAction<FamilyFormData>>;
  onSubmit: (data: FamilyFormData) => void;
  isCompleted?: boolean;
  isExpanded?: boolean;
}

export default function FamilyInformationStep({
  formData,
  setFormData,
  onSubmit,
  isCompleted = false,
  isExpanded = false,
}: FamilyInformationStepProps) {
  const [accordionValue, setAccordionValue] = useState<string>('');

  const handleSubmit = () => {
    onSubmit(formData);
  };

  // Update accordion state when isExpanded prop changes (from sidebar navigation)
  useEffect(() => {
    if (isExpanded) {
      setAccordionValue('item-1');
    }
  }, [isExpanded]);
  return (
    <div className="space-y-8">
      <Accordion
        type="single"
        collapsible
        value={accordionValue}
        onValueChange={setAccordionValue}
        className="space-y-4"
      >
        <AccordionItem value="item-1" className="border-2 border-stone-200 rounded-md">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <CheckCircle className={`h-6 w-6 ${isCompleted ? 'text-black' : 'text-stone-600'}`} />
              <span className="text-lg font-semibold">1.1 Family Information</span>
              <Badge className={isCompleted ? 'bg-black text-white' : 'bg-stone-600 text-white'}>
                {isCompleted ? 'Completed' : 'In Progress'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle>Family Information</CardTitle>
                <CardDescription>
                  Please provide your family details and contact information for the Au Pair program.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 lg:space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="family-name">Family Name</Label>
                    <Input
                      id="family-name"
                      placeholder="Enter family surname"
                      className="mt-1"
                      value={formData.familyName}
                      onChange={e => setFormData(prev => ({ ...prev, familyName: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="family-address">Home Address</Label>
                    <Input
                      id="family-address"
                      placeholder="Enter complete address"
                      className="mt-1"
                      value={formData.address}
                      onChange={e => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="postal-code">Postal Code</Label>
                    <Input
                      id="postal-code"
                      placeholder="1234 AB"
                      className="mt-1"
                      value={formData.postalCode}
                      onChange={e => setFormData(prev => ({ ...prev, postalCode: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      placeholder="Enter city"
                      className="mt-1"
                      value={formData.city}
                      onChange={e => setFormData(prev => ({ ...prev, city: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="country">Country</Label>
                    <Input
                      id="country"
                      defaultValue="Netherlands"
                      className="mt-1"
                      value={formData.country}
                      onChange={e => setFormData(prev => ({ ...prev, country: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      placeholder="+31 6 12345678"
                      className="mt-1"
                      value={formData.phone}
                      onChange={e => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      className="mt-1"
                      value={formData.email}
                      onChange={e => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-semibold">Children</Label>
                  </div>

                  {(!formData.children || formData.children.length === 0) && (
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-md p-6 text-center">
                      <Users className="mx-auto h-8 w-8 text-muted-foreground mb-3" />
                      <h3 className="text-base font-semibold mb-2">No children added yet</h3>
                      <p className="text-muted-foreground mb-4 text-sm">
                        Click "Add Another Child" to start adding your children's information
                      </p>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setFormData(prev => ({
                            ...prev,
                            children: [{ name: '', age: '', gender: '' }],
                          }));
                        }}
                      >
                        <Users className="h-4 w-4 mr-2" />
                        Add First Child
                      </Button>
                    </div>
                  )}

                  {formData.children &&
                    formData.children.map((child, index) => (
                      <Card key={index} className="border-2">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base">Child {index + 1}</CardTitle>
                            {formData.children.length > 1 && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const newChildren = formData.children.filter((_, i) => i !== index);
                                  setFormData(prev => ({ ...prev, children: newChildren }));
                                }}
                                className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                              >
                                ×
                              </Button>
                            )}
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                            <div>
                              <Label htmlFor={`child-name-${index}`} className="text-sm font-medium">
                                Child Name
                              </Label>
                              <Input
                                id={`child-name-${index}`}
                                placeholder="Enter child's name"
                                value={child.name}
                                onChange={e => {
                                  const newChildren = [...formData.children];
                                  newChildren[index] = { ...newChildren[index], name: e.target.value };
                                  setFormData(prev => ({ ...prev, children: newChildren }));
                                }}
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label htmlFor={`child-age-${index}`} className="text-sm font-medium">
                                Age
                              </Label>
                              <Input
                                id={`child-age-${index}`}
                                type="number"
                                placeholder="Age"
                                min="0"
                                max="18"
                                value={child.age}
                                onChange={e => {
                                  const newChildren = [...formData.children];
                                  newChildren[index] = { ...newChildren[index], age: e.target.value };
                                  setFormData(prev => ({ ...prev, children: newChildren }));
                                }}
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label htmlFor={`child-gender-${index}`} className="text-sm font-medium">
                                Gender
                              </Label>
                              <Select
                                value={child.gender}
                                onValueChange={value => {
                                  const newChildren = [...formData.children];
                                  newChildren[index] = { ...newChildren[index], gender: value };
                                  setFormData(prev => ({ ...prev, children: newChildren }));
                                }}
                              >
                                <SelectTrigger className="mt-1">
                                  <SelectValue placeholder="Select gender" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="boy">Boy</SelectItem>
                                  <SelectItem value="girl">Girl</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
                <div className="flex items-center justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newChildren = [...(formData.children || []), { name: '', age: '', gender: '' }];
                      setFormData(prev => ({ ...prev, children: newChildren }));
                    }}
                    className="h-8"
                  >
                    <Users className="h-3 w-3 mr-2" />
                    Add Another Child
                  </Button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="preferred-start">Preferred Start Date</Label>
                    <Input
                      id="preferred-start"
                      type="date"
                      className="mt-1"
                      value={formData.preferredStartDate}
                      onChange={e => setFormData(prev => ({ ...prev, preferredStartDate: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="preferred-start">Latest Start Date</Label>
                    <Input
                      id="preferred-start"
                      type="date"
                      className="mt-1"
                      value={formData.latestStartDate}
                      onChange={e => setFormData(prev => ({ ...prev, latestStartDate: e.target.value }))}
                    />
                  </div>
                </div>

                <Button className="w-full" onClick={handleSubmit}>
                  Save Family Information
                </Button>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
